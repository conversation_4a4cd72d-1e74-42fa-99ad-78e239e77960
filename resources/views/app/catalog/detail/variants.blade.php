@if(!empty($productVariants) && count($productVariants) > 1)
<!-- Product Variants Start -->
<div class="product-variants-section mb-4">
    <h5 class="variants-title mb-3">Dostępne warianty:</h5>
    <div class="variants-grid">
        @foreach($productVariants as $variant)
            <div class="variant-item {{ $variant['is_current'] ? 'current-variant' : '' }}">
                @if($variant['is_current'])
                    <!-- Current product - not clickable -->
                    <div class="variant-content current">
                        <div class="variant-image">
                            <img src="{{ $variant['product']->thumb }}" 
                                 alt="{{ $variant['product']->name }}" 
                                 class="img-fluid">
                        </div>
                        <div class="variant-info">
                            <div class="variant-difference">
                                {{ $variant['difference'] ?: 'Standard' }}
                            </div>
                            <div class="variant-price">
                                @if ($variant['product']->price != $variant['product']->discounted_price)
                                    <span class="original-price">{{ number_format($variant['product']->price, 2, ',', ' ') }} zł</span>
                                    <span class="discounted-price">{{ number_format($variant['product']->discounted_price, 2, ',', ' ') }} zł</span>
                                @else
                                    <span class="price">{{ number_format($variant['product']->price, 2, ',', ' ') }} zł</span>
                                @endif
                            </div>
                            <div class="current-badge">Aktualny</div>
                        </div>
                    </div>
                @else
                    <!-- Other variants - clickable -->
                    <a href="{{ route('detail', ['path' => $variant['product']->category->url, 'product' => $variant['product']]) }}" 
                       class="variant-content variant-link">
                        <div class="variant-image">
                            <img src="{{ $variant['product']->thumb }}" 
                                 alt="{{ $variant['product']->name }}" 
                                 class="img-fluid">
                        </div>
                        <div class="variant-info">
                            <div class="variant-difference">
                                {{ $variant['difference'] ?: 'Standard' }}
                            </div>
                            <div class="variant-price">
                                @if ($variant['product']->price != $variant['product']->discounted_price)
                                    <span class="original-price">{{ number_format($variant['product']->price, 2, ',', ' ') }} zł</span>
                                    <span class="discounted-price">{{ number_format($variant['product']->discounted_price, 2, ',', ' ') }} zł</span>
                                @else
                                    <span class="price">{{ number_format($variant['product']->price, 2, ',', ' ') }} zł</span>
                                @endif
                            </div>
                            @if($variant['product']->stock <= 0)
                                <div class="out-of-stock-badge">Brak w magazynie</div>
                            @endif
                        </div>
                    </a>
                @endif
            </div>
        @endforeach
    </div>
</div>

<style>
.product-variants-section {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    background-color: #f8f9fa;
}

.variants-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 15px;
}

.variants-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 15px;
}

.variant-item {
    position: relative;
}

.variant-content {
    display: block;
    text-decoration: none;
    color: inherit;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 12px;
    background-color: white;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.variant-link:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
    text-decoration: none;
    color: inherit;
    transform: translateY(-2px);
}

.variant-content.current {
    border-color: #28a745;
    background-color: #f8fff9;
}

.variant-image {
    text-align: center;
    margin-bottom: 8px;
    flex-shrink: 0;
}

.variant-image img {
    max-width: 60px;
    max-height: 60px;
    object-fit: contain;
}

.variant-info {
    text-align: center;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.variant-difference {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
    margin-bottom: 5px;
    min-height: 1.2em;
    line-height: 1.2;
    word-wrap: break-word;
}

.variant-price {
    font-size: 0.85rem;
    margin-bottom: 5px;
}

.variant-price .original-price {
    text-decoration: line-through;
    color: #6c757d;
    font-size: 0.8rem;
    display: block;
}

.variant-price .discounted-price,
.variant-price .price {
    color: #dc3545;
    font-weight: 600;
}

.current-badge {
    background-color: #28a745;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
}

.out-of-stock-badge {
    background-color: #dc3545;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .variants-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }
    
    .variant-content {
        padding: 8px;
    }
    
    .variant-image img {
        max-width: 50px;
        max-height: 50px;
    }
    
    .variant-difference {
        font-size: 0.8rem;
    }
    
    .variant-price {
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .variants-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}
</style>
<!-- Product Variants End -->
@endif
