@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">Product Variants</h2>
        <a href="{{ route('admin.product-variants.create') }}" class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
            Create New Variant
        </a>
    </div>

    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {{ session('success') }}
        </div>
    @endif

    <div class="overflow-x-auto">
        <table class="min-w-full table-auto">
            <thead>
                <tr class="bg-gray-100">
                    <th class="px-4 py-2 text-left">ID</th>
                    <th class="px-4 py-2 text-left">Name</th>
                    <th class="px-4 py-2 text-left">Products Count</th>
                    <th class="px-4 py-2 text-left">Created</th>
                    <th class="px-4 py-2 text-left">Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse($variants as $variant)
                    <tr class="border-b">
                        <td class="px-4 py-2">{{ $variant->id }}</td>
                        <td class="px-4 py-2">
                            {{ $variant->name ?: 'Unnamed Variant' }}
                        </td>
                        <td class="px-4 py-2">
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                                {{ $variant->products()->count() }} products
                            </span>
                        </td>
                        <td class="px-4 py-2">{{ $variant->created_at->format('Y-m-d H:i') }}</td>
                        <td class="px-4 py-2">
                            <div class="flex space-x-2">
                                <a href="{{ route('admin.product-variants.show', $variant) }}" 
                                   class="text-blue-500 hover:text-blue-700">View</a>
                                <a href="{{ route('admin.product-variants.edit', $variant) }}" 
                                   class="text-green-500 hover:text-green-700">Edit</a>
                                <form action="{{ route('admin.product-variants.destroy', $variant) }}" 
                                      method="POST" class="inline-block"
                                      onsubmit="return confirm('Are you sure you want to delete this variant? Products will be unlinked but not deleted.')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-500 hover:text-red-700">Delete</button>
                                </form>
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5" class="px-4 py-8 text-center text-gray-500">
                            No product variants found. <a href="{{ route('admin.product-variants.create') }}" class="text-blue-500">Create your first variant</a>.
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    @if(method_exists($variants, 'links'))
        <div class="mt-4">
            {{ $variants->links() }}
        </div>
    @endif
</div>
@endsection
