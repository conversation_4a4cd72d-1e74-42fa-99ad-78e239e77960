@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">Edit Product</h2>
        <a href="{{ route('admin.products.index') }}" class="text-blue-500 hover:text-blue-700">
            &larr; Back to Products
        </a>
    </div>

    <form action="{{ route('admin.products.update', $product->id) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        <!-- Product Fields -->
        <div class="mb-4">
            <label for="name" class="block text-gray-700">Name</label>
            <input type="text" id="name" name="name" class="w-full border rounded p-2" value="{{ old('name', $product->name) }}" required>
        </div>
        <div class="mb-4">
            <label for="description" class="block text-gray-700">Description</label>
            <textarea id="description" name="description" class="w-full border rounded p-2">{{ old('description', $product->description) }}"></textarea>
        </div>

        <!-- Category Selection -->
        <div class="mb-4">
            <label for="category_id" class="block text-gray-700">Category</label>
            <select id="category_id" name="category_id" class="w-full border rounded p-2">
                <option value="">Select a category</option>
                @foreach($categories as $category)
                    <option value="{{ $category->id }}" {{ $product->category_id == $category->id ? 'selected' : '' }}>
                        {{ $category->name }}
                    </option>
                @endforeach
            </select>
        </div>

        <!-- Pricing Section -->
        <div class="mb-4 p-4 bg-gray-50 rounded">
            <h3 class="text-lg font-semibold mb-2">Pricing</h3>
            
            @if($product->molos_price)
                <div class="mb-2">
                    <label class="block text-gray-700">Original Molos Price</label>
                    <span class="text-gray-600">${{ number_format($product->molos_price, 2) }}</span>
                </div>
            @endif
            
            <div class="mb-2">
                <label for="price_percentage" class="block text-gray-700">Price Percentage (%)</label>
                <input type="number" name="price_percentage" id="price_percentage" 
                       class="w-full border rounded p-2" min="0" max="1000" 
                       value="{{ old('price_percentage', $product->price_percentage) }}" 
                       placeholder="Leave empty to use category or default percentage">
                <p class="text-sm text-gray-500 mt-1">
                    Current effective percentage: {{ $product->getPricePercentage() }}%
                    @if($product->molos_price)
                        (Calculated price: ${{ number_format($product->calculatePriceFromMolos(), 2) }})
                    @endif
                </p>
            </div>
            
            <div class="mb-2">
                <label class="block text-gray-700">Current Price</label>
                <span class="text-lg font-semibold">${{ number_format($product->price, 2) }}</span>
            </div>
        </div>

        <!-- Producent Selection -->
        <div class="mb-4">
            <label for="producent_id" class="block text-gray-700">Producent</label>
            <select id="producent_id" name="producent_id" class="w-full border rounded p-2">
                <option value="">Select a producent</option>
                @foreach($producents as $producent)
                    <option value="{{ $producent->id }}" {{ $product->producent_id == $producent->id ? 'selected' : '' }}>
                        {{ $producent->name }}
                    </option>
                @endforeach
            </select>
        </div>

        <!-- Properties -->
        <div class="mb-4">
            <label class="block text-gray-700">Properties</label>
            @foreach($properties as $property)
                <div class="mb-2">
                    <label for="property-{{ $property->id }}" class="block text-gray-600">{{ $property->name }}</label>
                    @if ($property->type == 'text')
                        @if ($property->is_multiple)
                            @foreach($product->properties->where('id', $property->id) as $prop)
                                <input type="text" id="property-{{ $property->id }}" name="properties[{{ $property->id }}][value][]" class="w-full border rounded p-2 mb-2" value="{{ old('properties.'.$property->id.'.value', $prop->pivot->value) }}">
                            @endforeach
                            <input type="text" id="property-{{ $property->id }}" name="properties[{{ $property->id }}][value][]" class="w-full border rounded p-2 mb-2" placeholder="Add another value">
                        @else
                            <input type="text" id="property-{{ $property->id }}" name="properties[{{ $property->id }}][value]" class="w-full border rounded p-2" value="{{ old('properties.'.$property->id.'.value', $product->properties->where('id', $property->id)->first()->pivot->value ?? '') }}">
                        @endif
                    @elseif ($property->type == 'select')
                        @if ($property->is_multiple)
                            <select id="property-{{ $property->id }}" name="properties[{{ $property->id }}][property_option_id][]" class="w-full border rounded p-2" multiple>
                                @foreach ($property->options as $option)
                                    <option value="{{ $option->id }}" {{ in_array($option->id, old('properties.'.$property->id.'.property_option_id', $product->properties->where('id', $property->id)->pluck('pivot.property_option_id')->toArray() ?? [])) ? 'selected' : '' }}>
                                        {{ $option->value }}
                                    </option>
                                @endforeach
                            </select>
                        @else
                            <select id="property-{{ $property->id }}" name="properties[{{ $property->id }}][property_option_id]" class="w-full border rounded p-2">
                                <option value="">Select an option</option>
                                @foreach ($property->options as $option)
                                    @php
                                        $selectedOptionId = old('properties.'.$property->id.'.property_option_id', $product->properties->where('id', $property->id)->first()->pivot->property_option_id ?? null);
                                    @endphp
                                    <option value="{{ $option->id }}" {{ $option->id == $selectedOptionId ? 'selected' : '' }}>
                                        {{ $option->value }}
                                    </option>
                                @endforeach
                            </select>
                        @endif
                    @endif
                </div>
            @endforeach
        </div>

        <!-- Main Image -->
        <div class="mb-4">
            <label for="main_image" class="block text-gray-700">Main Image</label>
            @if($product->getFirstMediaUrl('images'))
                <img src="{{ $product->getFirstMediaUrl('images', 'thumb') }}" alt="Main Image" class="mb-2" style="max-height: 100px;">
            @endif
            <input type="file" id="main_image" name="main_image" class="w-full border rounded p-2">
        </div>

        <!-- Stock Field (Read-only) -->
        <div class="mb-4">
            <label for="stock" class="block text-gray-700">Stock</label>
            <input type="number" id="stock" name="stock" class="w-full border rounded p-2 bg-gray-100" 
                value="{{ old('stock', $product->stock) }}" readonly>
        </div>

        <!-- Gallery Images -->
        <div class="mb-4">
            <label for="gallery_images" class="block text-gray-700">Gallery Images</label>
            <div class="flex flex-wrap gap-2 mb-2">
                @foreach($product->getMedia('gallery') as $media)
                    <div class="relative" id="media-{{ $media->id }}">
                        <img src="{{ $media->getUrl('thumb') }}" alt="Gallery Image" 
                            class="rounded" style="max-height: 100px;">
                        <button type="button" 
                            class="absolute top-0 right-0 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center"
                            onclick="deleteGalleryImage({{ $media->id }})">×</button>
                    </div>
                @endforeach
            </div>
            <input type="file" id="gallery_images" name="gallery_images[]" 
                class="w-full border rounded p-2" multiple accept="image/*">
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-between">
            <a href="{{ route('admin.products.index') }}" class="bg-gray-300 text-gray-700 py-2 px-4 rounded">Cancel</a>
            <button type="submit" name="action" value="update" class="bg-blue-500 text-white py-2 px-4 rounded">Update</button>
            <button type="submit" name="action" value="save" class="bg-green-500 text-white py-2 px-4 rounded">Save</button>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
function deleteGalleryImage(mediaId) {
    if (confirm('Are you sure you want to delete this image?')) {
        $.ajax({
            url: '/admin/products/media/' + mediaId,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    $('#media-' + mediaId).remove();
                }
            }
        });
    }
}
</script>
@endpush
