@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <a href="{{ route('admin.properties.index') }}" class="text-blue-500 hover:text-blue-700">
            &larr; Back to Properties
        </a>
        <h2 class="text-xl font-bold">Create Property</h2>
    </div>

    <form action="{{ route('admin.properties.store') }}" method="POST">
        @csrf
        <div class="mb-4">
            <label for="name" class="block text-gray-700">Name</label>
            <input type="text" id="name" name="name" class="w-full border rounded p-2" required>
        </div>
        <div class="mb-4">
            <label for="type" class="block text-gray-700">Type</label>
            <select id="type" name="type" class="w-full border rounded p-2">
                <option value="text">Text</option>
                <option value="select">Select</option>
            </select>
        </div>
        <div class="mb-4">
            <label for="is_multiple" class="block text-gray-700">Allow Multiple Values</label>
            <input type="checkbox" id="is_multiple" name="is_multiple" value="1" class="form-checkbox">
        </div>
        <div class="mb-4">
            <label for="categories" class="block text-gray-700">Categories</label>
            <select id="categories" name="categories[]" class="w-full border rounded p-2" multiple size="15">
                @foreach ($categories as $category)
                    <option value="{{ $category->id }}" {{ in_array($category->id, old('categories', [])) ? 'selected' : '' }}>
                        {{ $category->name }}
                    </option>
                @endforeach
            </select>
            <small class="text-gray-500">Hold Ctrl (Cmd on Mac) to select multiple categories. Leave empty to show for all categories.</small>
        </div>
        <div class="mb-4">
            <label for="options" class="block text-gray-700">Options (for select type)</label>
            <input type="text" id="options" name="options[]" class="w-full border rounded p-2 mb-2" placeholder="Option 1">
            <input type="text" name="options[]" class="w-full border rounded p-2 mb-2" placeholder="Option 2">
            <input type="text" name="options[]" class="w-full border rounded p-2 mb-2" placeholder="Option 3">
            <!-- Add more inputs as needed -->
        </div>
        <div class="flex justify-between">
            <a href="{{ route('admin.properties.index') }}" class="bg-gray-300 text-gray-700 py-2 px-4 rounded">Cancel</a>
            <button type="submit" name="action" value="create" class="bg-blue-500 text-white py-2 px-4 rounded">Create</button>
        </div>
    </form>
</div>
@endsection
