@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <h2 class="text-xl font-bold mb-4">Properties</h2>
    <a href="{{ route('admin.properties.create') }}" class="bg-blue-500 text-white py-2 px-4 rounded">Add Property</a>
    <table class="min-w-full bg-white mt-4">
        <thead>
            <tr>
                <th class="py-2 text-left">Name</th>
                <th class="py-2 text-left">Type</th>
                <th class="py-2 text-left">Multiple</th>
                <th class="py-2 text-left">Categories</th>
                <th class="py-2 text-left">Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($properties as $property)
            <tr class="border-b">
                <td class="py-2">{{ $property->name }}</td>
                <td class="py-2">
                    <span class="px-2 py-1 text-xs rounded {{ $property->type === 'select' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}">
                        {{ ucfirst($property->type) }}
                    </span>
                </td>
                <td class="py-2">
                    @if($property->is_multiple)
                        <span class="px-2 py-1 text-xs rounded bg-purple-100 text-purple-800">Yes</span>
                    @else
                        <span class="px-2 py-1 text-xs rounded bg-gray-100 text-gray-800">No</span>
                    @endif
                </td>
                <td class="py-2">
                    @if($property->categories->count() > 0)
                        <div class="flex flex-wrap gap-1">
                            @foreach($property->categories->take(3) as $category)
                                <span class="px-2 py-1 text-xs rounded bg-yellow-100 text-yellow-800">
                                    {{ $category->name }}
                                </span>
                            @endforeach
                            @if($property->categories->count() > 3)
                                <span class="px-2 py-1 text-xs rounded bg-gray-100 text-gray-600">
                                    +{{ $property->categories->count() - 3 }} more
                                </span>
                            @endif
                        </div>
                    @else
                        <span class="text-gray-500 text-sm">All categories</span>
                    @endif
                </td>
                <td class="py-2">
                    <a href="{{ route('admin.properties.edit', $property->id) }}" class="text-blue-500 hover:text-blue-700">Edit</a>
                    <form action="{{ route('admin.properties.destroy', $property->id) }}" method="POST" class="inline-block ml-2">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="text-red-500 hover:text-red-700" onclick="return confirm('Are you sure you want to delete this property?')">Delete</button>
                    </form>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
</div>
@endsection
