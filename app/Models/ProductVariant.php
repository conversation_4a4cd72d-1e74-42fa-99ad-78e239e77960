<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\HasFiltersAndSorts;

class ProductVariant extends Model
{
    use HasFactory, HasFiltersAndSorts;

    protected $fillable = [
        'name',
        'show_name',
    ];

    protected static $filters = [
        'name' => ['type' => 'text'],
    ];

    protected static $sorts = [
        'name',
        'created_at',
    ];

    /**
     * Get all products that belong to this variant group
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get the count of products in this variant group
     */
    public function getProductCountAttribute()
    {
        return $this->products()->count();
    }

    /**
     * Get products with stock available
     */
    public function availableProducts()
    {
        return $this->products()->where('stock', '>', 0)->where('status', true);
    }

    /**
     * Get the main product (first one or one marked as default)
     */
    public function getMainProductAttribute()
    {
        return $this->products()->orderBy('created_at')->first();
    }

    /**
     * Get variant differences for display
     */
    public function getVariantDifferences()
    {
        $products = $this->products()->get();
        
        if ($products->isEmpty()) {
            return [];
        }

        $variants = [];
        
        foreach ($products as $product) {
            $variants[] = [
                'product' => $product,
                'difference' => $product->variant_name ?: 'Standard',
                'is_current' => false, // This will be set dynamically in the view
            ];
        }

        return $variants;
    }
}
