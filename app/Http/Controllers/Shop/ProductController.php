<?php

namespace App\Http\Controllers\Shop;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Services\ProductService;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    protected $productService;

    public function __construct(ProductService $productService)
    {
        $this->productService = $productService;
    }

    /**
     * Display the product detail page.
     *
     * @param Request $request
     * @param string $path
     * @param Product $product
     * @return \Illuminate\View\View
     */
    public function detail(Request $request, $path, Product $product)
    {
        // Optionally, you can verify that the product belongs to the category represented by $path.
        // This ensures that the URL is valid and the product is in the correct category.

        // Example: Check if the product's category path matches the $path parameter.
        // Assuming you have a method in your Product model to get the full category path.
        // dd($product->category->full_path);
        // if ($product->category->full_path !== $path) {
        //     abort(404);
        // }

        // Load any relationships needed for the view.
        $product->load(['category', 'producent']);
        // dd($product->getMedia('images'));

        // Find similar products (variants)
        $similarProducts = $this->productService->findSimilarProducts($product);

        // Get variant differences for display
        $productVariants = [];
        if ($similarProducts->isNotEmpty()) {
            $productVariants = $this->productService->getVariantDifferences($product, $similarProducts);
        }

        // Pass the product and variants to the view.
        return view('app.pages.detail', compact('product', 'productVariants'));
    }
}
