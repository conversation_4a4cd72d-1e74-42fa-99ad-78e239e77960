<?php

namespace App\Http\Controllers\Admin;

use App\Models\ProductVariant;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProductVariantController extends AdminController
{
    public function index()
    {
        list($variants, $filters, $sorts) = $this->applyPaginationAndFiltering(ProductVariant::query());
        
        return view('admin.product-variants.index', compact('variants', 'filters', 'sorts'));
    }

    public function create()
    {
        return view('admin.product-variants.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'nullable|string|max:255',
            'show_name' => 'nullable|string|max:255',
        ]);

        $variant = ProductVariant::create([
            'name' => $request->name,
            'show_name' => $request->show_name,
        ]);

        return redirect()->route('admin.product-variants.edit', $variant)
                        ->with('success', 'Product variant created successfully. Now add products to this variant.');
    }

    public function show(ProductVariant $productVariant)
    {
        $productVariant->load('products');
        return view('admin.product-variants.show', compact('productVariant'));
    }

    public function edit(ProductVariant $productVariant)
    {
        $productVariant->load('products');
        
        // Get products for selection (excluding those already in this variant)
        $availableProducts = Product::whereNull('product_variant_id')
                                  ->orWhere('product_variant_id', $productVariant->id)
                                  ->with(['category', 'producent'])
                                  ->paginate(20);

        return view('admin.product-variants.edit', compact('productVariant', 'availableProducts'));
    }

    public function update(Request $request, ProductVariant $productVariant)
    {
        $request->validate([
            'name' => 'nullable|string|max:255',
            'show_name' => 'nullable|string|max:255',
        ]);

        $productVariant->update([
            'name' => $request->name,
            'show_name' => $request->show_name,
        ]);

        return redirect()->route('admin.product-variants.edit', $productVariant)
                        ->with('success', 'Product variant updated successfully.');
    }

    public function destroy(ProductVariant $productVariant)
    {
        // Remove variant association from products
        $productVariant->products()->update([
            'product_variant_id' => null,
            'variant_name' => null,
        ]);

        $productVariant->delete();

        return redirect()->route('admin.product-variants.index')
                        ->with('success', 'Product variant deleted successfully.');
    }

    /**
     * Add a product to the variant
     */
    public function addProduct(Request $request, ProductVariant $productVariant)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
        ]);

        $product = Product::findOrFail($request->product_id);
        
        // Remove from any existing variant
        if ($product->product_variant_id) {
            $product->update(['product_variant_id' => null, 'variant_name' => null]);
        }

        // Add to this variant
        $product->update(['product_variant_id' => $productVariant->id]);

        return response()->json(['success' => true, 'message' => 'Product added to variant successfully.']);
    }

    /**
     * Remove a product from the variant
     */
    public function removeProduct(Request $request, ProductVariant $productVariant)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
        ]);

        $product = Product::findOrFail($request->product_id);
        
        if ($product->product_variant_id == $productVariant->id) {
            $product->update([
                'product_variant_id' => null,
                'variant_name' => null,
            ]);
        }

        return response()->json(['success' => true, 'message' => 'Product removed from variant successfully.']);
    }

    /**
     * Update variant name for a specific product
     */
    public function updateProductVariantName(Request $request, ProductVariant $productVariant)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'variant_name' => 'nullable|string|max:255',
        ]);

        $product = Product::findOrFail($request->product_id);
        
        if ($product->product_variant_id == $productVariant->id) {
            $product->update(['variant_name' => $request->variant_name]);
        }

        return response()->json(['success' => true, 'message' => 'Variant name updated successfully.']);
    }

    /**
     * Search products for adding to variant
     */
    public function searchProducts(Request $request, ProductVariant $productVariant)
    {
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        
        $query = Product::with(['category', 'producent'])
                       ->where(function($q) use ($search) {
                           if ($search) {
                               $q->where('name', 'like', '%' . $search . '%')
                                 ->orWhere('sku', 'like', '%' . $search . '%');
                           }
                       })
                       ->whereNull('product_variant_id')
                       ->orWhere('product_variant_id', $productVariant->id);

        $products = $query->paginate(20, ['*'], 'page', $page);

        return response()->json([
            'products' => $products->items(),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
            ]
        ]);
    }


}
